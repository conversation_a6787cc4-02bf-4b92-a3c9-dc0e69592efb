"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/config/features.ts":
/*!********************************!*\
  !*** ./src/config/features.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACTION_TO_FEATURE_MAP: () => (/* binding */ ACTION_TO_FEATURE_MAP),\n/* harmony export */   ACTION_TYPES: () => (/* binding */ ACTION_TYPES),\n/* harmony export */   ACTIVITY_TO_FEATURE_MAP: () => (/* binding */ ACTIVITY_TO_FEATURE_MAP),\n/* harmony export */   FEATURES_CONFIG: () => (/* binding */ FEATURES_CONFIG),\n/* harmony export */   FEATURE_IDS: () => (/* binding */ FEATURE_IDS),\n/* harmony export */   PLAN_RESTRICTED_ROUTES: () => (/* binding */ PLAN_RESTRICTED_ROUTES),\n/* harmony export */   actionToFeature: () => (/* binding */ actionToFeature),\n/* harmony export */   activityToFeature: () => (/* binding */ activityToFeature),\n/* harmony export */   featureRequiresPayment: () => (/* binding */ featureRequiresPayment),\n/* harmony export */   getAllFeatureIds: () => (/* binding */ getAllFeatureIds),\n/* harmony export */   getAllFeatures: () => (/* binding */ getAllFeatures),\n/* harmony export */   getFeatureConfig: () => (/* binding */ getFeatureConfig),\n/* harmony export */   getFeatureDisplayName: () => (/* binding */ getFeatureDisplayName),\n/* harmony export */   getFeatureTokensRequired: () => (/* binding */ getFeatureTokensRequired),\n/* harmony export */   getFeaturesByCategory: () => (/* binding */ getFeaturesByCategory),\n/* harmony export */   getFeaturesForPlan: () => (/* binding */ getFeaturesForPlan),\n/* harmony export */   isValidFeatureId: () => (/* binding */ isValidFeatureId)\n/* harmony export */ });\n// src/config/features.ts\n// Configuración centralizada de características y funcionalidades\n// ============================================================================\n// CONSTANTES DE FEATURES\n// ============================================================================\n/**\n * Identificadores únicos de características del sistema\n */ const FEATURE_IDS = {\n    DOCUMENT_UPLOAD: 'document_upload',\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_TUTOR_CHAT: 'ai_tutor_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_A1_A2: 'summary_a1_a2'\n};\n/**\n * Acciones que pueden realizarse en el sistema\n */ const ACTION_TYPES = {\n    TEST_GENERATION: 'test_generation',\n    FLASHCARD_GENERATION: 'flashcard_generation',\n    MIND_MAP_GENERATION: 'mind_map_generation',\n    AI_CHAT: 'ai_chat',\n    STUDY_PLANNING: 'study_planning',\n    SUMMARY_GENERATION: 'summary_generation'\n};\n/**\n * Configuración de todas las características del sistema\n */ const FEATURES_CONFIG = {\n    [FEATURE_IDS.DOCUMENT_UPLOAD]: {\n        id: FEATURE_IDS.DOCUMENT_UPLOAD,\n        name: 'document_upload',\n        displayName: 'Subida de documentos',\n        description: 'Permite subir y procesar documentos PDF para estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 0,\n        icon: 'FiUpload',\n        route: '/app'\n    },\n    [FEATURE_IDS.TEST_GENERATION]: {\n        id: FEATURE_IDS.TEST_GENERATION,\n        name: 'test_generation',\n        displayName: 'Generación de tests',\n        description: 'Genera tests automáticos basados en el contenido de estudio',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 5000,\n        icon: 'FiFileText',\n        route: '/app/tests'\n    },\n    [FEATURE_IDS.FLASHCARD_GENERATION]: {\n        id: FEATURE_IDS.FLASHCARD_GENERATION,\n        name: 'flashcard_generation',\n        displayName: 'Generación de flashcards',\n        description: 'Crea flashcards inteligentes para memorización efectiva',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 3000,\n        icon: 'FiLayers',\n        route: '/app/flashcards'\n    },\n    [FEATURE_IDS.MIND_MAP_GENERATION]: {\n        id: FEATURE_IDS.MIND_MAP_GENERATION,\n        name: 'mind_map_generation',\n        displayName: 'Generación de mapas mentales',\n        description: 'Genera mapas mentales visuales para mejor comprensión',\n        category: 'core',\n        minimumPlans: [\n            'free',\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: false,\n        tokensRequired: 4000,\n        icon: 'FiGitBranch',\n        route: '/app/mindmaps'\n    },\n    [FEATURE_IDS.AI_TUTOR_CHAT]: {\n        id: FEATURE_IDS.AI_TUTOR_CHAT,\n        name: 'ai_tutor_chat',\n        displayName: 'Chat con preparador IA',\n        description: 'Interactúa con un preparador de oposiciones inteligente',\n        category: 'premium',\n        minimumPlans: [\n            'usuario',\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 2000,\n        icon: 'FiMessageSquare',\n        route: '/app/ai-tutor'\n    },\n    [FEATURE_IDS.STUDY_PLANNING]: {\n        id: FEATURE_IDS.STUDY_PLANNING,\n        name: 'study_planning',\n        displayName: 'Planificación de estudios',\n        description: 'Crea planes de estudio personalizados y estructurados',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 20000,\n        icon: 'FiCalendar',\n        route: '/plan-estudios'\n    },\n    [FEATURE_IDS.SUMMARY_A1_A2]: {\n        id: FEATURE_IDS.SUMMARY_A1_A2,\n        name: 'summary_a1_a2',\n        displayName: 'Resúmenes A1 y A2',\n        description: 'Genera resúmenes especializados para oposiciones A1 y A2',\n        category: 'advanced',\n        minimumPlans: [\n            'pro'\n        ],\n        requiresPayment: true,\n        tokensRequired: 6000,\n        icon: 'FiBook',\n        route: '/app/summaries'\n    }\n};\n// ============================================================================\n// MAPEOS Y UTILIDADES\n// ============================================================================\n/**\n * Mapeo de acciones a características\n */ const ACTION_TO_FEATURE_MAP = {\n    [ACTION_TYPES.TEST_GENERATION]: FEATURE_IDS.TEST_GENERATION,\n    [ACTION_TYPES.FLASHCARD_GENERATION]: FEATURE_IDS.FLASHCARD_GENERATION,\n    [ACTION_TYPES.MIND_MAP_GENERATION]: FEATURE_IDS.MIND_MAP_GENERATION,\n    [ACTION_TYPES.AI_CHAT]: FEATURE_IDS.AI_TUTOR_CHAT,\n    [ACTION_TYPES.STUDY_PLANNING]: FEATURE_IDS.STUDY_PLANNING,\n    [ACTION_TYPES.SUMMARY_GENERATION]: FEATURE_IDS.SUMMARY_A1_A2\n};\n/**\n * Mapeo de actividades de tokens a características\n */ const ACTIVITY_TO_FEATURE_MAP = {\n    'test_generation': FEATURE_IDS.TEST_GENERATION,\n    'flashcard_generation': FEATURE_IDS.FLASHCARD_GENERATION,\n    'mind_map_generation': FEATURE_IDS.MIND_MAP_GENERATION,\n    'ai_chat': FEATURE_IDS.AI_TUTOR_CHAT,\n    'study_planning': FEATURE_IDS.STUDY_PLANNING,\n    'summary_generation': FEATURE_IDS.SUMMARY_A1_A2,\n    'document_analysis': FEATURE_IDS.DOCUMENT_UPLOAD\n};\n/**\n * Configuración de rutas restringidas por plan\n */ const PLAN_RESTRICTED_ROUTES = {\n    '/plan-estudios': [\n        'pro'\n    ],\n    '/app/ai-tutor': [\n        'usuario',\n        'pro'\n    ],\n    '/app/summaries': [\n        'pro'\n    ],\n    '/app/advanced-features': [\n        'pro'\n    ]\n};\n// ============================================================================\n// FUNCIONES UTILITARIAS\n// ============================================================================\n/**\n * Obtiene la configuración de una característica\n */ function getFeatureConfig(featureId) {\n    return FEATURES_CONFIG[featureId];\n}\n/**\n * Obtiene el nombre para mostrar de una característica\n */ function getFeatureDisplayName(featureId) {\n    const config = FEATURES_CONFIG[featureId];\n    return config?.displayName || featureId;\n}\n/**\n * Obtiene todas las características de una categoría\n */ function getFeaturesByCategory(category) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.category === category);\n}\n/**\n * Obtiene las características disponibles para un plan\n */ function getFeaturesForPlan(planId) {\n    return Object.values(FEATURES_CONFIG).filter((feature)=>feature.minimumPlans.includes(planId));\n}\n/**\n * Verifica si una característica requiere pago\n */ function featureRequiresPayment(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.requiresPayment || false;\n}\n/**\n * Obtiene los tokens requeridos para una característica\n */ function getFeatureTokensRequired(featureId) {\n    const config = getFeatureConfig(featureId);\n    return config?.tokensRequired || 0;\n}\n/**\n * Convierte una acción a su característica correspondiente\n */ function actionToFeature(action) {\n    return ACTION_TO_FEATURE_MAP[action];\n}\n/**\n * Convierte una actividad a su característica correspondiente\n */ function activityToFeature(activity) {\n    return ACTIVITY_TO_FEATURE_MAP[activity];\n}\n/**\n * Obtiene todas las características como array\n */ function getAllFeatures() {\n    return Object.values(FEATURES_CONFIG);\n}\n/**\n * Obtiene los IDs de todas las características\n */ function getAllFeatureIds() {\n    return Object.keys(FEATURES_CONFIG);\n}\n/**\n * Verifica si un ID de característica es válido\n */ function isValidFeatureId(featureId) {\n    return featureId in FEATURES_CONFIG;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/config/features.ts\n");

/***/ })

});