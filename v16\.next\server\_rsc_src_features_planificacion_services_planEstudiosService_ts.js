"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_features_planificacion_services_planEstudiosService_ts";
exports.ids = ["_rsc_src_features_planificacion_services_planEstudiosService_ts"];
exports.modules = {

/***/ "(rsc)/./src/features/planificacion/services/planEstudiosService.ts":
/*!********************************************************************!*\
  !*** ./src/features/planificacion/services/planEstudiosService.ts ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activarPlan: () => (/* binding */ activarPlan),\n/* harmony export */   actualizarNotasPlan: () => (/* binding */ actualizarNotasPlan),\n/* harmony export */   eliminarPlan: () => (/* binding */ eliminarPlan),\n/* harmony export */   guardarPlanEstudios: () => (/* binding */ guardarPlanEstudios),\n/* harmony export */   guardarPlanEstudiosServidor: () => (/* binding */ guardarPlanEstudiosServidor),\n/* harmony export */   guardarProgresoTarea: () => (/* binding */ guardarProgresoTarea),\n/* harmony export */   obtenerEstadisticasProgreso: () => (/* binding */ obtenerEstadisticasProgreso),\n/* harmony export */   obtenerHistorialPlanes: () => (/* binding */ obtenerHistorialPlanes),\n/* harmony export */   obtenerProgresoPlan: () => (/* binding */ obtenerProgresoPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(rsc)/./src/lib/supabase/authService.ts\");\n\n\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para cliente)\n */ async function guardarPlanEstudios(temarioId, planData, titulo) {\n    try {\n        // Usar cliente del navegador\n        const { user: clientUser, error: authError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!clientUser || authError) {\n            console.error('No hay usuario autenticado');\n            return null;\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').insert([\n            {\n                user_id: clientUser.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios:', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente:', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios:', error);\n        return null;\n    }\n}\n/**\n * Guarda un plan de estudios generado en la base de datos (versión para servidor)\n */ async function guardarPlanEstudiosServidor(temarioId, planData, user, titulo) {\n    try {\n        const { createServerSupabaseClient } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\"));\n        const supabase = await createServerSupabaseClient();\n        const { data, error } = await supabase.from('planes_estudios').insert([\n            {\n                user_id: user.id,\n                temario_id: temarioId,\n                titulo: titulo || 'Plan de Estudios',\n                plan_data: planData,\n                activo: true,\n                version: 1\n            }\n        ]).select().single();\n        if (error) {\n            console.error('Error al guardar plan de estudios (servidor):', error);\n            return null;\n        }\n        console.log('✅ Plan de estudios guardado exitosamente (servidor):', data.id);\n        return data.id;\n    } catch (error) {\n        console.error('Error al guardar plan de estudios (servidor):', error);\n        return null;\n    }\n}\n/**\n * Obtiene todos los planes de estudios de un temario (historial)\n */ async function obtenerHistorialPlanes(temarioId) {\n    try {\n        const { user, error: authError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('*').eq('user_id', user.id).eq('temario_id', temarioId).order('fecha_generacion', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error al obtener historial de planes:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener historial de planes:', error);\n        return [];\n    }\n}\n/**\n * Actualiza las notas de un plan de estudios\n */ async function actualizarNotasPlan(planId, notas) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            notas,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al actualizar notas del plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al actualizar notas del plan:', error);\n        return false;\n    }\n}\n/**\n * Marca un plan como activo y desactiva los demás\n */ async function activarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').update({\n            activo: true,\n            actualizado_en: new Date().toISOString()\n        }).eq('id', planId);\n        if (error) {\n            console.error('Error al activar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al activar plan:', error);\n        return false;\n    }\n}\n/**\n * Elimina un plan de estudios\n */ async function eliminarPlan(planId) {\n    try {\n        const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').delete().eq('id', planId);\n        if (error) {\n            console.error('Error al eliminar plan:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al eliminar plan:', error);\n        return false;\n    }\n}\n/**\n * Guarda el progreso de una tarea del plan\n */ async function guardarProgresoTarea(planId, semanaNúmero, diaNombre, tareaTitulo, tareaTipo, completado, tiempoRealMinutos, notasProgreso, calificacion) {\n    try {\n        const { user, error: authError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return false;\n        }\n        // Verificar si ya existe un registro de progreso para esta tarea\n        const { data: existente } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('id').eq('plan_id', planId).eq('user_id', user.id).eq('semana_numero', semanaNúmero).eq('dia_nombre', diaNombre).eq('tarea_titulo', tareaTitulo).single();\n        if (existente) {\n            // Actualizar registro existente\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').update({\n                completado,\n                fecha_completado: completado ? new Date().toISOString() : null,\n                tiempo_real_minutos: tiempoRealMinutos,\n                notas_progreso: notasProgreso,\n                calificacion,\n                actualizado_en: new Date().toISOString()\n            }).eq('id', existente.id);\n            if (error) {\n                console.error('Error al actualizar progreso:', error);\n                return false;\n            }\n        } else {\n            // Crear nuevo registro\n            const { error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').insert([\n                {\n                    plan_id: planId,\n                    user_id: user.id,\n                    semana_numero: semanaNúmero,\n                    dia_nombre: diaNombre,\n                    tarea_titulo: tareaTitulo,\n                    tarea_tipo: tareaTipo,\n                    completado,\n                    fecha_completado: completado ? new Date().toISOString() : null,\n                    tiempo_real_minutos: tiempoRealMinutos,\n                    notas_progreso: notasProgreso,\n                    calificacion\n                }\n            ]);\n            if (error) {\n                console.error('Error al crear progreso:', error);\n                return false;\n            }\n        }\n        return true;\n    } catch (error) {\n        console.error('Error al guardar progreso de tarea:', error);\n        return false;\n    }\n}\n/**\n * Obtiene el progreso de un plan de estudios\n */ async function obtenerProgresoPlan(planId) {\n    try {\n        const { user, error: authError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user || authError) {\n            return [];\n        }\n        const { data, error } = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_plan_estudios').select('*').eq('plan_id', planId).eq('user_id', user.id).order('semana_numero', {\n            ascending: true\n        }).order('creado_en', {\n            ascending: true\n        });\n        if (error) {\n            console.error('Error al obtener progreso del plan:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error al obtener progreso del plan:', error);\n        return [];\n    }\n}\n/**\n * Obtiene estadísticas del progreso del plan\n */ async function obtenerEstadisticasProgreso(planId) {\n    try {\n        const progreso = await obtenerProgresoPlan(planId);\n        const plan = await _lib_supabase_client__WEBPACK_IMPORTED_MODULE_0__.supabase.from('planes_estudios').select('plan_data').eq('id', planId).single();\n        if (!plan.data) {\n            return {\n                totalTareas: 0,\n                tareasCompletadas: 0,\n                porcentajeCompletado: 0,\n                tiempoTotalEstimado: 0,\n                tiempoTotalReal: 0,\n                semanasCompletadas: 0,\n                totalSemanas: 0\n            };\n        }\n        const planData = plan.data.plan_data;\n        const totalSemanas = planData.semanas.length;\n        // Calcular total de tareas\n        let totalTareas = 0;\n        planData.semanas.forEach((semana)=>{\n            semana.dias.forEach((dia)=>{\n                totalTareas += dia.tareas.length;\n            });\n        });\n        const tareasCompletadas = progreso.filter((p)=>p.completado).length;\n        const porcentajeCompletado = totalTareas > 0 ? tareasCompletadas / totalTareas * 100 : 0;\n        const tiempoTotalReal = progreso.filter((p)=>p.tiempo_real_minutos).reduce((total, p)=>total + (p.tiempo_real_minutos || 0), 0);\n        // Calcular semanas completadas (todas las tareas de la semana completadas)\n        let semanasCompletadas = 0;\n        planData.semanas.forEach((semana)=>{\n            const tareasSemanaTotales = semana.dias.reduce((total, dia)=>total + dia.tareas.length, 0);\n            const tareasSemanCompletadas = progreso.filter((p)=>p.semana_numero === semana.numero && p.completado).length;\n            if (tareasSemanaTotales > 0 && tareasSemanCompletadas === tareasSemanaTotales) {\n                semanasCompletadas++;\n            }\n        });\n        return {\n            totalTareas,\n            tareasCompletadas,\n            porcentajeCompletado: Math.round(porcentajeCompletado * 100) / 100,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal,\n            semanasCompletadas,\n            totalSemanas\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas de progreso:', error);\n        return {\n            totalTareas: 0,\n            tareasCompletadas: 0,\n            porcentajeCompletado: 0,\n            tiempoTotalEstimado: 0,\n            tiempoTotalReal: 0,\n            semanasCompletadas: 0,\n            totalSemanas: 0\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/features/planificacion/services/planEstudiosService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(rsc)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\r\n * Inicia sesión con email y contraseña\r\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            error: errorMessage\n        };\n    }\n}\n/**\r\n * Cierra la sesión del usuario actual\r\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\r\n * Obtiene la sesión actual del usuario\r\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\r\n * Obtiene el usuario actual\r\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\r\n * Verifica si el usuario está autenticado\r\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            autoRefreshToken: true,\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0Q7QUFFcEQsc0RBQXNEO0FBQy9DLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUMsRUFDekM7UUFDRUksTUFBTTtZQUNKQyxnQkFBZ0I7WUFDaEJDLGtCQUFrQjtZQUNsQkMsb0JBQW9CLEtBQVEsOENBQThDO1FBQzVFO0lBQ0Y7QUFFSjtBQUVBLCtDQUErQztBQUN4QyxNQUFNQyxXQUFXVCxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdXBhYmFzZVxcY2xpZW50LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJztcblxuLy8gQ2xpZW50ZSBwYXJhIGVsIG5hdmVnYWRvciAoY29tcG9uZW50ZXMgZGVsIGNsaWVudGUpXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlQ2xpZW50KCkge1xuICByZXR1cm4gY3JlYXRlQnJvd3NlckNsaWVudChcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwhLFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZISxcbiAgICB7XG4gICAgICBhdXRoOiB7XG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLCAgICAgICAvLyBQZXJzaXN0aXIgc2VzacOzbiBlbiBlbCBuYXZlZ2Fkb3JcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogdHJ1ZSwgICAgIC8vIFJlZnJlc2NhciB0b2tlbiBhdXRvbcOhdGljYW1lbnRlXG4gICAgICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZSAgICAvLyBFU0VOQ0lBTDogRGV0ZWN0YXIgeSBwcm9jZXNhciB0b2tlbnMgZGUgVVJMXG4gICAgICB9XG4gICAgfVxuICApO1xufVxuXG4vLyBNYW50ZW5lciBjb21wYXRpYmlsaWRhZCBjb24gY8OzZGlnbyBleGlzdGVudGVcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuIl0sIm5hbWVzIjpbImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJjcmVhdGVDbGllbnQiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJhdXRoIiwicGVyc2lzdFNlc3Npb24iLCJhdXRvUmVmcmVzaFRva2VuIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(rsc)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _types_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/types/database */ \"(rsc)/./src/types/database.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n\n// NOTA: Para usar el cliente del servidor, importar directamente desde './server'\n// import { createServerSupabaseClient } from '@/lib/supabase/server';\n// Re-exportar todos los tipos desde el archivo centralizado de tipos de base de datos\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlL3N1cGFiYXNlQ2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSx5RUFBeUU7QUFDdkI7QUFFbEQsa0ZBQWtGO0FBQ2xGLHNFQUFzRTtBQUV0RSxzRkFBc0Y7QUFDckQiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXHYxNlxcc3JjXFxsaWJcXHN1cGFiYXNlXFxzdXBhYmFzZUNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTb2xvIHJlLWV4cG9ydGFyIGVsIGNsaWVudGUgZGVsIG5hdmVnYWRvciBwYXJhIG1hbnRlbmVyIGNvbXBhdGliaWxpZGFkXHJcbmV4cG9ydCB7IGNyZWF0ZUNsaWVudCwgc3VwYWJhc2UgfSBmcm9tICcuL2NsaWVudCc7XHJcblxyXG4vLyBOT1RBOiBQYXJhIHVzYXIgZWwgY2xpZW50ZSBkZWwgc2Vydmlkb3IsIGltcG9ydGFyIGRpcmVjdGFtZW50ZSBkZXNkZSAnLi9zZXJ2ZXInXHJcbi8vIGltcG9ydCB7IGNyZWF0ZVNlcnZlclN1cGFiYXNlQ2xpZW50IH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc2VydmVyJztcclxuXHJcbi8vIFJlLWV4cG9ydGFyIHRvZG9zIGxvcyB0aXBvcyBkZXNkZSBlbCBhcmNoaXZvIGNlbnRyYWxpemFkbyBkZSB0aXBvcyBkZSBiYXNlIGRlIGRhdG9zXHJcbmV4cG9ydCAqIGZyb20gJ0AvdHlwZXMvZGF0YWJhc2UnO1xyXG4iXSwibmFtZXMiOlsiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/types/database.ts":
/*!*******************************!*\
  !*** ./src/types/database.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/**\n * Tipos relacionados con la base de datos Supabase\n *\n * Este archivo contiene todas las interfaces y tipos que representan\n * las entidades de la base de datos y sus relaciones.\n */ // ============================================================================\n// TIPOS BÁSICOS Y ENUMS\n// ============================================================================\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/types/database.ts\n");

/***/ })

};
;