import { NextRequest, NextResponse } from 'next/server';
import { createServerSupabaseClient } from '@/lib/supabase/server';
import { generarTest, generarFlashcards, generarMapaMental, generarResumen } from '@/lib/gemini/index';
import { obtenerRespuestaIA } from '@/lib/gemini/questionService';
import { generarPlanEstudios } from '@/features/planificacion/services/planGeneratorService';
import { editarResumen } from '@/lib/gemini/resumenEditor';
import { ApiAIInputSchema } from '@/lib/zodSchemas';
// Los tokens se manejan automáticamente por tokenTracker.ts en las funciones de IA
import { PlanValidationService } from '@/lib/services/planValidation';
import { FreeAccountService } from '@/lib/services/freeAccountService';
import { ERROR_MESSAGES } from '@/config/constants';
import { SupabaseAdminService } from '@/lib/supabase/admin';

// Helper function to increment usage count for free accounts
// NOTA: Los tokens se incrementan automáticamente por tokenTracker.ts
// Este método solo incrementa contadores de features (tests, flashcards, mindMaps)
async function incrementUsageForFreeAccount(
  userId: string,
  feature: 'tests' | 'flashcards' | 'mindMaps',
  amount: number = 1
): Promise<void> {
  try {
    // Get user profile to check plan
    const profile = await SupabaseAdminService.getUserProfile(userId);

    if (profile?.subscription_plan === 'free') {
      const success = await FreeAccountService.incrementUsageCount(userId, feature, amount);
      if (!success) {
        console.warn(`⚠️ Failed to increment ${feature} usage count for user ${userId}`);
      } else {
        console.log(`✅ Incremented ${feature} usage by ${amount} for free user ${userId}`);
      }
    }
  } catch (error) {
    console.error(`❌ Error incrementing usage for user ${userId}:`, error);
  }
}

// API route for AI actions (formerly Gemini)
export async function POST(req: NextRequest) {
  let user = null;
  let body = null;

  console.log(`🔥 [API] POST /api/ai iniciado:`, {
    timestamp: new Date().toISOString(),
    url: req.url,
    method: req.method,
    headers: {
      'content-type': req.headers.get('content-type'),
      'user-agent': req.headers.get('user-agent')?.substring(0, 50) + '...',
      'cookie': req.headers.get('cookie') ? 'present' : 'missing'
    }
  });

  try {

    // Crear cliente de Supabase usando la implementación correcta
    console.log(`🔐 [API] Creando cliente Supabase...`);
    const supabase = await createServerSupabaseClient();

    console.log(`🔐 [API] Obteniendo usuario autenticado...`);
    const { data: { user: authUser }, error: userError } = await supabase.auth.getUser();
    user = authUser;

    if (!user) {
      console.error(`❌ [API] Usuario no autenticado:`, {
        userError: userError?.message,
        hasCookies: !!req.headers.get('cookie')
      });
      return NextResponse.json({
        error: ERROR_MESSAGES.UNAUTHORIZED,
        debug: {
          userError: userError?.message,
          hasCookies: !!req.headers.get('cookie')
        }
      }, { status: 401 });
    }

    console.log(`✅ [API] Usuario autenticado:`, { userId: user.id });

    console.log(`📥 [API] Parseando body de la petición...`);
    body = await req.json();

    console.log(`📋 [API] Body parseado:`, {
      hasAction: !!body.action,
      hasPeticion: !!body.peticion,
      hasContextos: !!body.contextos,
      hasCantidad: !!body.cantidad,
      hasPregunta: !!body.pregunta,
      hasDocumentos: !!body.documentos,
      bodyKeys: Object.keys(body),
      timestamp: new Date().toISOString()
    });

    // Validación robusta de entrada
    console.log(`🔍 [API] Validando entrada con schema...`);
    const parseResult = ApiAIInputSchema.safeParse(body);
    if (!parseResult.success) {
      console.error(`❌ [API] Validación de schema falló:`, parseResult.error.errors);
      return NextResponse.json({
        error: ERROR_MESSAGES.INVALID_DATA,
        detalles: parseResult.error.errors
      }, { status: 400 });
    }
    console.log(`✅ [API] Validación de schema exitosa`);


    // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA
    if (body.pregunta && body.documentos) {
      // Validar acceso al chat con IA para usuarios gratuitos
      const chatValidation = await PlanValidationService.canUserPerformAction(
        user.id,
        'ai_chat',
        1
      );

      if (!chatValidation.allowed) {
        return NextResponse.json({
          error: 'Acceso denegado: ' + chatValidation.reason
        }, { status: 403 });
      }

      const result = await obtenerRespuestaIA(body.pregunta, body.documentos);

      // ✅ CORREGIDO: No hacer tracking aquí porque ya se hace en llamarOpenAI()
      // El tracking se realiza automáticamente en openaiClient.ts con datos reales

      return NextResponse.json({ result });
    }

    const { action, peticion, contextos, cantidad, temarioId } = body;

    let result;

    switch (action) {
      case 'generarTest':
        // Validar acceso a generación de tests
        const testValidation = await PlanValidationService.canUserPerformAction(
          user.id,
          'test_generation',
          cantidad || 1
        );

        if (!testValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + testValidation.reason
          }, { status: 403 });
        }

        result = await generarTest(peticion, contextos, cantidad);

        // Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'tests', cantidad || 1);
        break;
      case 'generarFlashcards':
        // Validar acceso a generación de flashcards
        const flashcardValidation = await PlanValidationService.canUserPerformAction(
          user.id,
          'flashcard_generation',
          cantidad || 1
        );

        if (!flashcardValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + flashcardValidation.reason
          }, { status: 403 });
        }

        result = await generarFlashcards(peticion, contextos, cantidad);

        // Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'flashcards', cantidad || 1);
        break;
      case 'generarMapaMental':
        // Validar acceso a generación de mapas mentales
        const mindMapValidation = await PlanValidationService.canUserPerformAction(
          user.id,
          'mind_map_generation',
          1
        );

        if (!mindMapValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + mindMapValidation.reason
          }, { status: 403 });
        }

        result = await generarMapaMental(peticion, contextos);

        // Incrementar contador de uso para cuentas gratuitas
        await incrementUsageForFreeAccount(user.id, 'mindMaps', 1);
        break;
      case 'generarResumen':
        // Validar acceso a generación de resúmenes
        const summaryValidation = await PlanValidationService.canUserPerformAction(
          user.id,
          'summary_generation',
          1
        );

        if (!summaryValidation.allowed) {
          return NextResponse.json({
            error: 'Acceso denegado: ' + summaryValidation.reason
          }, { status: 403 });
        }

        // Para resúmenes, esperamos que contextos[0] sea el contenido del documento
        if (!contextos || contextos.length !== 1) {
          throw new Error('Se requiere exactamente un documento para generar un resumen');
        }

        // Crear objeto documento a partir del contexto
        const documento = {
          titulo: peticion.split('|')[0] || 'Documento sin título',
          contenido: contextos[0],
          categoria: peticion.split('|')[1],
          numero_tema: peticion.split('|')[2] ? parseInt(peticion.split('|')[2]) : undefined
        };

        const instrucciones = peticion.split('|')[3] || undefined;
        result = await generarResumen(documento, instrucciones);
        break;
      case 'editarResumen':
        if (!contextos[0]) {
          throw new Error('Se requiere el contenido del resumen para editarlo');
        }

        result = await editarResumen(contextos[0]);
        break;
      case 'generarPlanEstudios':
        console.log(`📚 [API] Iniciando generación de plan de estudios...`);

        // Validar acceso a planificación de estudios
        console.log(`🔍 [API] Validando acceso a planificación...`);

        // Obtener tokens requeridos de la configuración de features
        const { getFeatureTokensRequired, FEATURE_IDS } = await import('@/config/features');
        const requiredTokens = getFeatureTokensRequired(FEATURE_IDS.STUDY_PLANNING);

        console.log(`🔧 [API] Configuración de tokens para study_planning:`, {
          featureId: FEATURE_IDS.STUDY_PLANNING,
          tokensRequiredFromConfig: requiredTokens,
          timestamp: new Date().toISOString()
        });

        console.log(`🔍 [API] Parámetros de validación:`, {
          userId: user.id,
          feature: 'study_planning',
          tokensRequiredFromConfig: requiredTokens,
          tokensRequestedForValidation: requiredTokens,
          timestamp: new Date().toISOString()
        });

        const planValidation = await PlanValidationService.canUserPerformAction(
          user.id,
          'study_planning',
          requiredTokens
        );

        console.log(`📊 [API] Resultado de validación de planificación:`, {
          allowed: planValidation.allowed,
          reason: planValidation.reason,
          remainingUsage: planValidation.remainingUsage,
          planLimits: planValidation.planLimits,
          timestamp: new Date().toISOString()
        });

        if (!planValidation.allowed) {
          console.error(`❌ [API] Acceso denegado a planificación:`, planValidation.reason);
          return NextResponse.json({
            error: 'Acceso denegado: ' + planValidation.reason
          }, { status: 403 });
        }
        console.log(`✅ [API] Acceso a planificación validado`);

        // Para planes de estudios, el temarioId viene en peticion
        const temarioIdFromPeticion = peticion || temarioId;
        if (!temarioIdFromPeticion) {
          console.error(`❌ [API] No se proporcionó temarioId`);
          throw new Error('Se requiere temarioId para generar el plan de estudios');
        }

        console.log(`🚀 [API] Llamando a generarPlanEstudios:`, {
          temarioId: temarioIdFromPeticion,
          userId: user.id,
          timestamp: new Date().toISOString()
        });

        console.log(`🚀 [API] Iniciando generarPlanEstudios para temario: ${temarioIdFromPeticion}`);
        const startTime = Date.now();
        result = await generarPlanEstudios(temarioIdFromPeticion, user);
        const executionTime = Date.now() - startTime;

        console.log(`✅ [API] generarPlanEstudios completado:`, {
          executionTime: `${executionTime}ms`,
          hasResult: !!result,
          resultType: typeof result,
          resultSemanas: result?.semanas?.length || 0,
          timestamp: new Date().toISOString()
        });

        console.log(`📤 [API] Enviando respuesta al cliente...`);
        break;
      default:
        return NextResponse.json({ error: 'Acción no soportada' }, { status: 400 });
    }

    // ✅ CORREGIDO: No hacer tracking aquí porque ya se hace en llamarOpenAI()
    // Cada función (generarTest, generarFlashcards, etc.) usa llamarOpenAI() internamente
    // que ya registra automáticamente el uso real de tokens con el modelo correcto

    return NextResponse.json({ result });

  } catch (error: any) {
    console.error('❌ Error en API AI:', {
      error,
      errorMessage: error?.message,
      errorStack: error?.stack,
      action: body?.action || 'unknown',
      temarioId: body?.temarioId || body?.peticion,
      userId: user?.id || 'unknown'
    });

    return NextResponse.json({
      error: 'Error interno del servidor',
      detalles: error.message
    }, { status: 500 });
  }
}
