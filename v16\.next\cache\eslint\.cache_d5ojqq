[{"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts": "157", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts": "158", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts": "159", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts": "160", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts": "161", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts": "162", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts": "163", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts": "164", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts": "165", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts": "166", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts": "167", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts": "168", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts": "169", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts": "170", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts": "171", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts": "172", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts": "173", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts": "174", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts": "175", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts": "176", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts": "177", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts": "178", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts": "179", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts": "180", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts": "181", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts": "182", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts": "183", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts": "184", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts": "185", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts": "186", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx": "187", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts": "188", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\BackgroundTasksPanel.tsx": "189", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\constants.ts": "190", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\features.ts": "191", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\index.ts": "192", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\plans.ts": "193", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useInactivityTimer.ts": "194", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanCalendario.ts": "195", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\api.ts": "196", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\database.ts": "197", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\features.ts": "198", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\index.ts": "199", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\ui.ts": "200", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\__tests__\\authService.test.ts": "201", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\__tests__\\conversacionesService.test.ts": "202", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\__tests__\\dashboardService.test.ts": "203", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\__tests__\\documentosService.test.ts": "204", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\__tests__\\FlashcardGenerator.test.tsx": "205", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\__tests__\\flashcardsService.test.ts": "206", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\__tests__\\mindmapsService.test.ts": "207", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\__tests__\\planificacionService.test.ts": "208", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\__tests__\\profileService.test.ts": "209", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\__tests__\\summariesService.test.ts": "210", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\__tests__\\temarioService.test.ts": "211", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\TestGenerator.test.tsx": "212", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\testsService.test.ts": "213", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\TestViewer.test.tsx": "214", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\limitHandler.test.ts": "215", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\permissionService.test.ts": "216", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\planValidation.test.ts": "217", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\setup\\testConfig.ts": "218", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\test-utils.tsx": "219", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\authFlow.test.tsx": "220", "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\documentFlow.test.tsx": "221"}, {"size": 8681, "mtime": 1750549865174, "results": "222", "hashOfConfig": "223"}, {"size": 8713, "mtime": 1749926277358, "results": "224", "hashOfConfig": "223"}, {"size": 5845, "mtime": 1750309142357, "results": "225", "hashOfConfig": "223"}, {"size": 3612, "mtime": 1750086603738, "results": "226", "hashOfConfig": "223"}, {"size": 6683, "mtime": 1750085064464, "results": "227", "hashOfConfig": "223"}, {"size": 5926, "mtime": 1750549829701, "results": "228", "hashOfConfig": "223"}, {"size": 8784, "mtime": 1750086580286, "results": "229", "hashOfConfig": "223"}, {"size": 8573, "mtime": 1750962780701, "results": "230", "hashOfConfig": "223"}, {"size": 7052, "mtime": 1750082610808, "results": "231", "hashOfConfig": "223"}, {"size": 1301, "mtime": 1750287727080, "results": "232", "hashOfConfig": "223"}, {"size": 5083, "mtime": 1750953765193, "results": "233", "hashOfConfig": "223"}, {"size": 4009, "mtime": 1750953752583, "results": "234", "hashOfConfig": "223"}, {"size": 2270, "mtime": 1750549762427, "results": "235", "hashOfConfig": "223"}, {"size": 9194, "mtime": 1750020486326, "results": "236", "hashOfConfig": "223"}, {"size": 271, "mtime": 1750308563029, "results": "237", "hashOfConfig": "223"}, {"size": 5932, "mtime": 1750106816513, "results": "238", "hashOfConfig": "223"}, {"size": 3019, "mtime": 1750549747348, "results": "239", "hashOfConfig": "223"}, {"size": 4781, "mtime": 1750547906770, "results": "240", "hashOfConfig": "223"}, {"size": 4139, "mtime": 1749829295626, "results": "241", "hashOfConfig": "223"}, {"size": 3765, "mtime": 1750309415770, "results": "242", "hashOfConfig": "223"}, {"size": 3987, "mtime": 1750107085364, "results": "243", "hashOfConfig": "223"}, {"size": 2245, "mtime": 1750086615997, "results": "244", "hashOfConfig": "223"}, {"size": 1120, "mtime": 1750110995546, "results": "245", "hashOfConfig": "223"}, {"size": 5431, "mtime": 1750283688975, "results": "246", "hashOfConfig": "223"}, {"size": 2214, "mtime": 1749910333290, "results": "247", "hashOfConfig": "223"}, {"size": 2110, "mtime": 1749913156452, "results": "248", "hashOfConfig": "223"}, {"size": 3432, "mtime": 1749830150148, "results": "249", "hashOfConfig": "223"}, {"size": 29205, "mtime": 1750953831833, "results": "250", "hashOfConfig": "223"}, {"size": 12130, "mtime": 1750519961058, "results": "251", "hashOfConfig": "223"}, {"size": 5086, "mtime": 1750254562388, "results": "252", "hashOfConfig": "223"}, {"size": 5649, "mtime": 1750544723119, "results": "253", "hashOfConfig": "223"}, {"size": 19613, "mtime": 1750866829931, "results": "254", "hashOfConfig": "223"}, {"size": 8147, "mtime": 1750075142671, "results": "255", "hashOfConfig": "223"}, {"size": 6679, "mtime": 1750020180985, "results": "256", "hashOfConfig": "223"}, {"size": 841, "mtime": 1750771975166, "results": "257", "hashOfConfig": "223"}, {"size": 6974, "mtime": 1750867022329, "results": "258", "hashOfConfig": "223"}, {"size": 11112, "mtime": 1750865170358, "results": "259", "hashOfConfig": "223"}, {"size": 12249, "mtime": 1750950266771, "results": "260", "hashOfConfig": "223"}, {"size": 9534, "mtime": 1750865465599, "results": "261", "hashOfConfig": "223"}, {"size": 16399, "mtime": 1750953843088, "results": "262", "hashOfConfig": "223"}, {"size": 7697, "mtime": 1750086750423, "results": "263", "hashOfConfig": "223"}, {"size": 5926, "mtime": 1750690443715, "results": "264", "hashOfConfig": "223"}, {"size": 12933, "mtime": 1750950493525, "results": "265", "hashOfConfig": "223"}, {"size": 7953, "mtime": 1750954150563, "results": "266", "hashOfConfig": "223"}, {"size": 8384, "mtime": 1750865830885, "results": "267", "hashOfConfig": "223"}, {"size": 2673, "mtime": 1749908591667, "results": "268", "hashOfConfig": "223"}, {"size": 6385, "mtime": 1750966156832, "results": "269", "hashOfConfig": "223"}, {"size": 7534, "mtime": 1750075103015, "results": "270", "hashOfConfig": "223"}, {"size": 9495, "mtime": 1750073106534, "results": "271", "hashOfConfig": "223"}, {"size": 8278, "mtime": 1750073147880, "results": "272", "hashOfConfig": "223"}, {"size": 6370, "mtime": 1750949816238, "results": "273", "hashOfConfig": "223"}, {"size": 2643, "mtime": 1749364222822, "results": "274", "hashOfConfig": "223"}, {"size": 3239, "mtime": 1750692677577, "results": "275", "hashOfConfig": "223"}, {"size": 3469, "mtime": 1750693988715, "results": "276", "hashOfConfig": "223"}, {"size": 4720, "mtime": 1750692751611, "results": "277", "hashOfConfig": "223"}, {"size": 8346, "mtime": 1750692897611, "results": "278", "hashOfConfig": "223"}, {"size": 5161, "mtime": 1750073161083, "results": "279", "hashOfConfig": "223"}, {"size": 2571, "mtime": 1750760269966, "results": "280", "hashOfConfig": "223"}, {"size": 56653, "mtime": 1750714886122, "results": "281", "hashOfConfig": "223"}, {"size": 9762, "mtime": 1750964454728, "results": "282", "hashOfConfig": "223"}, {"size": 4662, "mtime": 1750696077841, "results": "283", "hashOfConfig": "223"}, {"size": 2386, "mtime": 1748898729938, "results": "284", "hashOfConfig": "223"}, {"size": 8432, "mtime": 1750952578519, "results": "285", "hashOfConfig": "223"}, {"size": 3060, "mtime": 1750952591358, "results": "286", "hashOfConfig": "223"}, {"size": 1279, "mtime": 1748898005145, "results": "287", "hashOfConfig": "223"}, {"size": 2089, "mtime": 1748784425494, "results": "288", "hashOfConfig": "223"}, {"size": 3905, "mtime": 1750862535382, "results": "289", "hashOfConfig": "223"}, {"size": 11443, "mtime": 1750862546425, "results": "290", "hashOfConfig": "223"}, {"size": 21485, "mtime": 1750953854072, "results": "291", "hashOfConfig": "223"}, {"size": 14701, "mtime": 1749642914607, "results": "292", "hashOfConfig": "223"}, {"size": 15797, "mtime": 1748789194243, "results": "293", "hashOfConfig": "223"}, {"size": 11388, "mtime": 1749655003060, "results": "294", "hashOfConfig": "223"}, {"size": 6561, "mtime": 1749655003060, "results": "295", "hashOfConfig": "223"}, {"size": 3252, "mtime": 1750966187461, "results": "296", "hashOfConfig": "223"}, {"size": 10604, "mtime": 1750963947006, "results": "297", "hashOfConfig": "223"}, {"size": 2048, "mtime": 1749137653402, "results": "298", "hashOfConfig": "223"}, {"size": 4361, "mtime": 1749655003075, "results": "299", "hashOfConfig": "223"}, {"size": 4261, "mtime": 1748777250685, "results": "300", "hashOfConfig": "223"}, {"size": 6739, "mtime": 1749137786700, "results": "301", "hashOfConfig": "223"}, {"size": 8975, "mtime": 1750865875037, "results": "302", "hashOfConfig": "223"}, {"size": 6275, "mtime": 1748381218646, "results": "303", "hashOfConfig": "223"}, {"size": 11220, "mtime": 1748562362912, "results": "304", "hashOfConfig": "223"}, {"size": 20912, "mtime": 1750966272357, "results": "305", "hashOfConfig": "223"}, {"size": 1500, "mtime": 1748376642824, "results": "306", "hashOfConfig": "223"}, {"size": 10380, "mtime": 1748563534405, "results": "307", "hashOfConfig": "223"}, {"size": 4612, "mtime": 1748778081572, "results": "308", "hashOfConfig": "223"}, {"size": 15473, "mtime": 1749642914618, "results": "309", "hashOfConfig": "223"}, {"size": 4918, "mtime": 1748789236100, "results": "310", "hashOfConfig": "223"}, {"size": 383, "mtime": 1750952250657, "results": "311", "hashOfConfig": "223"}, {"size": 13248, "mtime": 1750953875614, "results": "312", "hashOfConfig": "223"}, {"size": 3779, "mtime": 1748557840096, "results": "313", "hashOfConfig": "223"}, {"size": 4463, "mtime": 1750719906831, "results": "314", "hashOfConfig": "223"}, {"size": 11230, "mtime": 1750964532131, "results": "315", "hashOfConfig": "223"}, {"size": 20926, "mtime": 1750865923199, "results": "316", "hashOfConfig": "223"}, {"size": 16874, "mtime": 1750866029504, "results": "317", "hashOfConfig": "223"}, {"size": 13033, "mtime": 1750719717802, "results": "318", "hashOfConfig": "223"}, {"size": 7378, "mtime": 1750862399705, "results": "319", "hashOfConfig": "223"}, {"size": 10458, "mtime": 1750862409883, "results": "320", "hashOfConfig": "223"}, {"size": 20287, "mtime": 1749364863869, "results": "321", "hashOfConfig": "223"}, {"size": 6272, "mtime": 1750862420020, "results": "322", "hashOfConfig": "223"}, {"size": 4912, "mtime": 1750719594922, "results": "323", "hashOfConfig": "223"}, {"size": 7055, "mtime": 1750719029925, "results": "324", "hashOfConfig": "223"}, {"size": 10727, "mtime": 1750718826516, "results": "325", "hashOfConfig": "223"}, {"size": 11016, "mtime": 1750086795224, "results": "326", "hashOfConfig": "223"}, {"size": 16089, "mtime": 1750257232905, "results": "327", "hashOfConfig": "223"}, {"size": 10652, "mtime": 1750866373520, "results": "328", "hashOfConfig": "223"}, {"size": 11515, "mtime": 1750110978404, "results": "329", "hashOfConfig": "223"}, {"size": 1408, "mtime": 1750952925299, "results": "330", "hashOfConfig": "223"}, {"size": 8677, "mtime": 1750866247630, "results": "331", "hashOfConfig": "223"}, {"size": 5212, "mtime": 1749655003075, "results": "332", "hashOfConfig": "223"}, {"size": 7498, "mtime": 1750952564244, "results": "333", "hashOfConfig": "223"}, {"size": 11098, "mtime": 1750696256433, "results": "334", "hashOfConfig": "223"}, {"size": 21535, "mtime": 1750760269966, "results": "335", "hashOfConfig": "223"}, {"size": 4528, "mtime": 1748810558456, "results": "336", "hashOfConfig": "223"}, {"size": 6444, "mtime": 1748810539098, "results": "337", "hashOfConfig": "223"}, {"size": 6524, "mtime": 1748810280408, "results": "338", "hashOfConfig": "223"}, {"size": 20502, "mtime": 1750862432534, "results": "339", "hashOfConfig": "223"}, {"size": 17180, "mtime": 1748900565639, "results": "340", "hashOfConfig": "223"}, {"size": 9265, "mtime": 1748900735287, "results": "341", "hashOfConfig": "223"}, {"size": 7562, "mtime": 1750862442484, "results": "342", "hashOfConfig": "223"}, {"size": 6378, "mtime": 1748877566234, "results": "343", "hashOfConfig": "223"}, {"size": 4512, "mtime": 1748630532621, "results": "344", "hashOfConfig": "223"}, {"size": 5574, "mtime": 1750862770953, "results": "345", "hashOfConfig": "223"}, {"size": 2905, "mtime": 1748630509095, "results": "346", "hashOfConfig": "223"}, {"size": 22856, "mtime": 1750953886262, "results": "347", "hashOfConfig": "223"}, {"size": 9383, "mtime": 1750862867014, "results": "348", "hashOfConfig": "223"}, {"size": 14034, "mtime": 1750865581974, "results": "349", "hashOfConfig": "223"}, {"size": 30941, "mtime": 1750865609942, "results": "350", "hashOfConfig": "223"}, {"size": 7297, "mtime": 1750696131484, "results": "351", "hashOfConfig": "223"}, {"size": 9239, "mtime": 1750865636993, "results": "352", "hashOfConfig": "223"}, {"size": 3333, "mtime": 1748376643113, "results": "353", "hashOfConfig": "223"}, {"size": 2060, "mtime": 1748814199030, "results": "354", "hashOfConfig": "223"}, {"size": 9266, "mtime": 1750692614862, "results": "355", "hashOfConfig": "223"}, {"size": 2651, "mtime": 1748561592350, "results": "356", "hashOfConfig": "223"}, {"size": 1337, "mtime": 1750107504888, "results": "357", "hashOfConfig": "223"}, {"size": 7023, "mtime": 1749416233417, "results": "358", "hashOfConfig": "223"}, {"size": 7540, "mtime": 1750021679298, "results": "359", "hashOfConfig": "223"}, {"size": 2314, "mtime": 1750761338786, "results": "360", "hashOfConfig": "223"}, {"size": 2407, "mtime": 1749364828132, "results": "361", "hashOfConfig": "223"}, {"size": 1086, "mtime": 1749329759055, "results": "362", "hashOfConfig": "223"}, {"size": 3253, "mtime": 1749655003091, "results": "363", "hashOfConfig": "223"}, {"size": 3527, "mtime": 1749364808623, "results": "364", "hashOfConfig": "223"}, {"size": 2587, "mtime": 1749364782712, "results": "365", "hashOfConfig": "223"}, {"size": 2046, "mtime": 1750695500164, "results": "366", "hashOfConfig": "223"}, {"size": 3077, "mtime": 1749655003091, "results": "367", "hashOfConfig": "223"}, {"size": 3887, "mtime": 1749364846856, "results": "368", "hashOfConfig": "223"}, {"size": 6123, "mtime": 1749364772281, "results": "369", "hashOfConfig": "223"}, {"size": 8426, "mtime": 1750086480511, "results": "370", "hashOfConfig": "223"}, {"size": 6508, "mtime": 1750086442416, "results": "371", "hashOfConfig": "223"}, {"size": 7674, "mtime": 1750086554604, "results": "372", "hashOfConfig": "223"}, {"size": 8509, "mtime": 1750086519480, "results": "373", "hashOfConfig": "223"}, {"size": 8535, "mtime": 1750086411730, "results": "374", "hashOfConfig": "223"}, {"size": 868, "mtime": 1750086640332, "results": "375", "hashOfConfig": "223"}, {"size": 1707, "mtime": 1750086372857, "results": "376", "hashOfConfig": "223"}, {"size": 13915, "mtime": 1750953897976, "results": "377", "hashOfConfig": "223"}, {"size": 17375, "mtime": 1750953619944, "results": "378", "hashOfConfig": "223"}, {"size": 12178, "mtime": 1750963925110, "results": "379", "hashOfConfig": "223"}, {"size": 11513, "mtime": 1750963902881, "results": "380", "hashOfConfig": "223"}, {"size": 31143, "mtime": 1750549685191, "results": "381", "hashOfConfig": "223"}, {"size": 18319, "mtime": 1750953908710, "results": "382", "hashOfConfig": "223"}, {"size": 426, "mtime": 1749138861932, "results": "383", "hashOfConfig": "223"}, {"size": 4254, "mtime": 1750963891035, "results": "384", "hashOfConfig": "223"}, {"size": 13571, "mtime": 1750550713790, "results": "385", "hashOfConfig": "223"}, {"size": 6717, "mtime": 1749655003091, "results": "386", "hashOfConfig": "223"}, {"size": 622, "mtime": 1750254594447, "results": "387", "hashOfConfig": "223"}, {"size": 9246, "mtime": 1749655003091, "results": "388", "hashOfConfig": "223"}, {"size": 6595, "mtime": 1749655003091, "results": "389", "hashOfConfig": "223"}, {"size": 1310, "mtime": 1748378808764, "results": "390", "hashOfConfig": "223"}, {"size": 4038, "mtime": 1749655003108, "results": "391", "hashOfConfig": "223"}, {"size": 11321, "mtime": 1749655003111, "results": "392", "hashOfConfig": "223"}, {"size": 23409, "mtime": 1750952351622, "results": "393", "hashOfConfig": "223"}, {"size": 313, "mtime": 1749655003114, "results": "394", "hashOfConfig": "223"}, {"size": 6748, "mtime": 1749655430336, "results": "395", "hashOfConfig": "223"}, {"size": 1576, "mtime": 1749479562972, "results": "396", "hashOfConfig": "223"}, {"size": 410, "mtime": 1750951862209, "results": "397", "hashOfConfig": "223"}, {"size": 12117, "mtime": 1750862913333, "results": "398", "hashOfConfig": "223"}, {"size": 8694, "mtime": 1750953741703, "results": "399", "hashOfConfig": "223"}, {"size": 16094, "mtime": 1750963936632, "results": "400", "hashOfConfig": "223"}, {"size": 7381, "mtime": 1750719054362, "results": "401", "hashOfConfig": "223"}, {"size": 10599, "mtime": 1750954063490, "results": "402", "hashOfConfig": "223"}, {"size": 10327, "mtime": 1750962732074, "results": "403", "hashOfConfig": "223"}, {"size": 7707, "mtime": 1750026605896, "results": "404", "hashOfConfig": "223"}, {"size": 2423, "mtime": 1749655955625, "results": "405", "hashOfConfig": "223"}, {"size": 16968, "mtime": 1750963913693, "results": "406", "hashOfConfig": "223"}, {"size": 13880, "mtime": 1750953641942, "results": "407", "hashOfConfig": "223"}, {"size": 4309, "mtime": 1749504407466, "results": "408", "hashOfConfig": "223"}, {"size": 11509, "mtime": 1749853487198, "results": "409", "hashOfConfig": "223"}, {"size": 12234, "mtime": 1749853536927, "results": "410", "hashOfConfig": "223"}, {"size": 7292, "mtime": 1750952915115, "results": "411", "hashOfConfig": "223"}, {"size": 8409, "mtime": 1750963464436, "results": "412", "hashOfConfig": "223"}, {"size": 8473, "mtime": 1750954052115, "results": "413", "hashOfConfig": "223"}, {"size": 3569, "mtime": 1750963775985, "results": "414", "hashOfConfig": "223"}, {"size": 7180, "mtime": 1750953571726, "results": "415", "hashOfConfig": "223"}, {"size": 3675, "mtime": 1750964364147, "results": "416", "hashOfConfig": "223"}, {"size": 8868, "mtime": 1750964433239, "results": "417", "hashOfConfig": "223"}, {"size": 2333, "mtime": 1750952549249, "results": "418", "hashOfConfig": "223"}, {"size": 9021, "mtime": 1750968953391, "results": "419", "hashOfConfig": "223"}, {"size": 1855, "mtime": 1750952196618, "results": "420", "hashOfConfig": "223"}, {"size": 507, "mtime": 1750951568713, "results": "421", "hashOfConfig": "223"}, {"size": 3685, "mtime": 1750952526474, "results": "422", "hashOfConfig": "223"}, {"size": 1100, "mtime": 1750966456281, "results": "423", "hashOfConfig": "223"}, {"size": 1084, "mtime": 1750966513623, "results": "424", "hashOfConfig": "223"}, {"size": 1130, "mtime": 1750966494989, "results": "425", "hashOfConfig": "223"}, {"size": 1217, "mtime": 1750966485168, "results": "426", "hashOfConfig": "223"}, {"size": 2218, "mtime": 1750966528482, "results": "427", "hashOfConfig": "223"}, {"size": 1274, "mtime": 1750966466171, "results": "428", "hashOfConfig": "223"}, {"size": 1246, "mtime": 1750966647918, "results": "429", "hashOfConfig": "223"}, {"size": 1241, "mtime": 1750966504971, "results": "430", "hashOfConfig": "223"}, {"size": 1222, "mtime": 1750966612995, "results": "431", "hashOfConfig": "223"}, {"size": 1213, "mtime": 1750966629141, "results": "432", "hashOfConfig": "223"}, {"size": 1043, "mtime": 1750966637988, "results": "433", "hashOfConfig": "223"}, {"size": 2288, "mtime": 1750966541478, "results": "434", "hashOfConfig": "223"}, {"size": 1401, "mtime": 1750966476105, "results": "435", "hashOfConfig": "223"}, {"size": 2098, "mtime": 1750966602746, "results": "436", "hashOfConfig": "223"}, {"size": 11479, "mtime": 1750966735836, "results": "437", "hashOfConfig": "223"}, {"size": 9859, "mtime": 1750966783580, "results": "438", "hashOfConfig": "223"}, {"size": 8681, "mtime": 1750966829172, "results": "439", "hashOfConfig": "223"}, {"size": 7835, "mtime": 1750967500671, "results": "440", "hashOfConfig": "223"}, {"size": 6443, "mtime": 1750968516808, "results": "441", "hashOfConfig": "223"}, {"size": 13519, "mtime": 1750967612949, "results": "442", "hashOfConfig": "223"}, {"size": 14140, "mtime": 1750967663744, "results": "443", "hashOfConfig": "223"}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "17uv4wy", {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\admin\\reactivate\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\cleanup-expired-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\email-failures\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\notification-stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\process-expired-grace-periods\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\reactivate-user\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\admin\\send-grace-period-reminders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\free-account-status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\generate-password-reset\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\pre-register-paid\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\register-free\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\auth\\resend-confirmation\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\document\\upload\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\notify-signup\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\payment\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\create-checkout-session\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\stripe\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\tokens\\purchase\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\cancel-subscription\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\notifications\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\plan\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\profile\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\reactivate\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\user\\validate-access\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\callback\\page.tsx", [], ["1107"], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirm-reset\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\confirmed\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\reset-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\auth\\unauthorized\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\debug-user\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\payment-pending\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\plan-estudios\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\thank-you\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\upgrade-plan\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\welcome\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\auth\\PlanValidationWrapper.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\thank-you\\ProcessingState.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\DocumentLimitStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountGuard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountStatus.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\FreeAccountTimer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\PlanCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\SessionInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenProgressBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenStatsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\TokenUsageChart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UnauthorizedAccess.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\UpgradePlanMessage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\openai.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\prompts.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\contexts\\BackgroundTasksContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\AuthManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ForgotPasswordModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\InactivityWarning.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\components\\SessionIndicator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\ConversationSidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\components\\QuestionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\components\\StudyStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\services\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\components\\DocumentUploader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardCollectionView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyMode.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardStudyOptions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\FlashcardViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\RevisionHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\components\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\components\\MindMapHelp.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\CalendarioModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanCalendario.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanEstudiosViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\PlanificacionAsistente.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\components\\TareasDelDia.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosClientService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planEstudiosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planGeneratorService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\services\\planificacionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\types\\calendarTypes.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\calendarioPreferences.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\utils\\planDateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\AccountSettings.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\NotificationHistory.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\components\\PlanUsage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\DiagnosticPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\MobileDebugInfo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\SidebarMenu.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\components\\SummaryList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemaEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioManager.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemarioSetup.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\components\\TemariosPredefinidosSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temarioService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\services\\temariosPredefinidosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestDetailedStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestEditModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGeneralStatistics.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestGenerator.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoConfig.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestRepasoViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\components\\TestViewer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useBackgroundGeneration.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useFreeAccount.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useMobileAuth.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanEstudiosResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanLimits.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useTaskResults.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useUserPlan.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\ai\\tokenTracker.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\auth\\validateUserAccess.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\formSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\flashcardGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\geminiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\mindMapGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\questionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenEditor.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\resumenGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\gemini\\testGenerator.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\openai\\openaiClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailAnalytics.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailNotificationService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailSender.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\email\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\freeAccountService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\limitHandler.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\permissionService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\planValidation.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\stripeWebhookHandlers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\services\\userManagement.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\stripe\\plans.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\admin.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\authService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\conversacionesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\dashboardService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\documentosService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\estadisticasService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\flashcardsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\resumenesService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\supabaseClient.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\testsService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.server.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\supabase\\tokenUsageService.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\emailTemplates.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\securityHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\utils\\webhookLogger.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\zodSchemas.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\middleware.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\scripts\\validateSystem.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\utils\\markdownToHTML.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\hooks\\usePlanLimits.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\paymentFlow.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\components\\ui\\BackgroundTasksPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\constants.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\features.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\config\\plans.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\useInactivityTimer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\hooks\\usePlanCalendario.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\database.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\features.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\types\\ui.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\auth\\__tests__\\authService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\conversations\\__tests__\\conversacionesService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\dashboard\\__tests__\\dashboardService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\documents\\__tests__\\documentosService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\__tests__\\FlashcardGenerator.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\flashcards\\__tests__\\flashcardsService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\mindmaps\\__tests__\\mindmapsService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\planificacion\\__tests__\\planificacionService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\profile\\__tests__\\profileService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\summaries\\__tests__\\summariesService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\temario\\__tests__\\temarioService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\TestGenerator.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\testsService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\tests\\__tests__\\TestViewer.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\limitHandler.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\permissionService.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\lib\\__tests__\\planValidation.test.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\setup\\testConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\test-utils.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\authFlow.test.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\__tests__\\integration\\documentFlow.test.tsx", [], [], {"ruleId": "1108", "severity": 1, "message": "1109", "line": 169, "column": 6, "nodeType": "1110", "endLine": 169, "endColumn": 28, "suggestions": "1111", "suppressions": "1112"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'status'. Either include it or remove the dependency array.", "ArrayExpression", ["1113"], ["1114"], {"desc": "1115", "fix": "1116"}, {"kind": "1117", "justification": "1118"}, "Update the dependencies array to be: [router, searchParams, status]", {"range": "1119", "text": "1120"}, "directive", "", [8806, 8828], "[router, searchParams, status]"]